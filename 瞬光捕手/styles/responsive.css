/* 瞬光捕手 - 响应式设计样式 */

/* 平板设备 (768px - 1024px) */
@media screen and (max-width: 1024px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .menu-btn {
        width: 220px;
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .game-header {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .overlay-content {
        padding: 1.5rem;
        max-width: 95%;
    }
    
    .setting-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .setting-group label {
        min-width: auto;
    }
    
    .setting-group select,
    .setting-group input[type="range"] {
        width: 100%;
        min-width: auto;
    }
}

/* 手机设备 (最大宽度 768px) */
@media screen and (max-width: 768px) {
    body {
        font-size: 14px;
    }
    
    .game-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .game-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .menu-btn {
        width: 200px;
        padding: 12px 20px;
        font-size: 0.95rem;
        margin: 8px auto;
    }
    
    .small-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    /* 游戏界面移动端适配 */
    .game-header {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        gap: 0.5rem;
    }
    
    .score-info, .level-info, .lives-info {
        font-size: 0.95rem;
    }
    
    #game-canvas {
        max-width: 95%;
        max-height: 60vh;
    }
    
    .game-hints {
        padding: 0.8rem;
    }
    
    #hint-text {
        font-size: 1rem;
    }
    
    /* 显示触摸控制 */
    .touch-controls {
        display: block;
    }
    
    .touch-area {
        width: 150px;
        height: 60px;
        font-size: 0.9rem;
    }
    
    /* 覆盖层移动端适配 */
    .overlay-content {
        padding: 1rem;
        margin: 1rem;
        max-width: calc(100% - 2rem);
        max-height: calc(100% - 2rem);
    }
    
    .overlay-content h2 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    
    /* 设置界面移动端适配 */
    .settings-content {
        max-width: 100%;
    }
    
    .setting-group {
        margin: 1rem 0;
        padding: 0.8rem;
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .setting-group label {
        text-align: left;
        margin-bottom: 5px;
    }
    
    .setting-group select {
        padding: 10px;
        font-size: 1rem;
    }
    
    .setting-group input[type="range"] {
        margin: 5px 0;
    }
    
    /* 玩家管理移动端适配 */
    .player-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        text-align: center;
    }
    
    .new-player input {
        padding: 12px;
        font-size: 1rem;
    }
    
    .current-player {
        flex-direction: column;
        gap: 8px;
    }
}

/* 小屏手机设备 (最大宽度 480px) */
@media screen and (max-width: 480px) {
    .game-title {
        font-size: 1.8rem;
    }
    
    .game-subtitle {
        font-size: 0.9rem;
    }
    
    .menu-btn {
        width: 180px;
        padding: 10px 15px;
        font-size: 0.9rem;
        margin: 6px auto;
    }
    
    .game-header {
        flex-direction: column;
        gap: 0.3rem;
        padding: 0.5rem;
        text-align: center;
    }
    
    .game-header > div {
        margin: 0.2rem 0;
    }
    
    #game-canvas {
        max-width: 98%;
        max-height: 50vh;
    }
    
    .touch-area {
        width: 120px;
        height: 50px;
        font-size: 0.8rem;
    }
    
    .overlay-content {
        padding: 0.8rem;
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
        max-height: calc(100% - 1rem);
    }
    
    .overlay-content h2 {
        font-size: 1.2rem;
    }
    
    .menu-buttons {
        margin: 1rem 0;
    }
    
    .final-score {
        font-size: 1.1rem;
        margin: 1rem 0;
    }
}

/* 横屏模式适配 */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .game-title {
        font-size: 1.5rem;
        margin-bottom: 0.3rem;
    }
    
    .game-subtitle {
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }
    
    .menu-btn {
        padding: 8px 15px;
        margin: 4px auto;
        font-size: 0.85rem;
    }
    
    .game-header {
        padding: 0.3rem 1rem;
        font-size: 0.8rem;
    }
    
    #game-canvas {
        max-height: 70vh;
    }
    
    .game-hints {
        padding: 0.5rem;
    }
    
    #hint-text {
        font-size: 0.9rem;
    }
    
    .overlay-content {
        padding: 1rem;
        max-height: 90vh;
    }
    
    .overlay-content h2 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }
    
    .setting-group {
        margin: 0.8rem 0;
        padding: 0.6rem;
    }
}

/* 超大屏幕适配 (1440px+) */
@media screen and (min-width: 1440px) {
    .game-title {
        font-size: 4rem;
    }
    
    .game-subtitle {
        font-size: 1.4rem;
    }
    
    .menu-btn {
        width: 300px;
        padding: 18px 35px;
        font-size: 1.2rem;
    }
    
    .game-header {
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
    }
    
    #game-canvas {
        max-width: 1200px;
        max-height: 800px;
    }
    
    .overlay-content {
        padding: 2.5rem;
        max-width: 600px;
    }
    
    .setting-group {
        padding: 1.2rem;
        margin: 2rem 0;
    }
}

/* 高分辨率屏幕优化 */
@media screen and (-webkit-min-device-pixel-ratio: 2), 
       screen and (min-resolution: 192dpi) {
    .game-title {
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }
    
    .menu-btn {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    #game-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 触摸设备特殊样式 */
@media (hover: none) and (pointer: coarse) {
    .menu-btn:hover {
        transform: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .small-btn:hover {
        transform: none;
        box-shadow: none;
    }
    
    .menu-btn:active {
        transform: scale(0.95);
    }
    
    .small-btn:active {
        transform: scale(0.9);
    }
    
    /* 增加触摸目标大小 */
    .menu-btn {
        min-height: 44px;
    }
    
    .small-btn {
        min-height: 36px;
        min-width: 60px;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid #ffffff;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    /* 已经是深色主题，保持现有样式 */
}

/* 浅色模式支持（如果需要） */
@media (prefers-color-scheme: light) {
    /* 可以在这里添加浅色主题样式 */
}
