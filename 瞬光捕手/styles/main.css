/* 瞬光捕手 - 主样式文件 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

/* 游戏容器 */
#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 通用屏幕样式 */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease-in-out;
}

.screen.hidden {
    opacity: 0;
    pointer-events: none;
}

/* 覆盖层样式 */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.3s ease-in-out;
}

.overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.overlay-content {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    text-align: center;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

/* 加载界面 */
#loading-screen {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 标题样式 */
.game-title {
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* 按钮样式 */
.menu-btn {
    display: block;
    width: 250px;
    padding: 15px 30px;
    margin: 10px auto;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.menu-btn.primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.small-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.small-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* 主菜单样式 */
.menu-content {
    text-align: center;
    max-width: 400px;
}

.menu-buttons {
    margin: 2rem 0;
}

.player-info {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.current-player {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

/* 游戏界面样式 */
#game-screen {
    padding: 0;
}

.game-ui {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
    gap: 1rem;
}

.score-info, .level-info, .lives-info {
    font-size: 1.1rem;
    font-weight: bold;
}

#game-canvas-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, #2a5298 0%, #1e3c72 100%);
}

#game-canvas {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    background: #000;
}

.game-hints {
    padding: 1rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

#hint-text {
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 触摸控制区域 */
.touch-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
}

.touch-area {
    width: 200px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.touch-area:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

/* 设置界面样式 */
.settings-content {
    max-width: 400px;
    width: 100%;
}

.setting-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.setting-group label {
    font-weight: bold;
    min-width: 80px;
}

.setting-group select,
.setting-group input[type="range"] {
    flex: 1;
    min-width: 150px;
}

.setting-group select {
    padding: 8px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.setting-group input[type="range"] {
    margin: 0 10px;
}

/* 玩家管理界面 */
.player-list, .new-player {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.player-list h3, .new-player h3 {
    margin-bottom: 1rem;
    color: #ffd700;
}

#existing-players {
    max-height: 200px;
    overflow-y: auto;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin: 0.5rem 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.player-item.active {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
}

.new-player {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.new-player input {
    padding: 10px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 排行榜界面样式 */
.leaderboard-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.leaderboard-tabs {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.leaderboard-list {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 2rem 0;
}

.leaderboard-header {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-entries {
    max-height: 400px;
    overflow-y: auto;
}

.leaderboard-entry {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.leaderboard-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.leaderboard-entry.current-player {
    background: rgba(76, 175, 80, 0.2);
    border-left: 4px solid #4CAF50;
}

.rank {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.rank.top-3 {
    color: #FFD700;
}

.rank.rank-1::before {
    content: "🥇";
    margin-right: 0.25rem;
}

.rank.rank-2::before {
    content: "🥈";
    margin-right: 0.25rem;
}

.rank.rank-3::before {
    content: "🥉";
    margin-right: 0.25rem;
}

.player-name {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.player-name .name {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.player-name .details {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.score {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    color: #4ecdc4;
}

.time {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.player-rank-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin: 2rem 0;
    text-align: center;
}

.player-rank {
    font-size: 1.1rem;
}

.player-rank .rank-text {
    color: #4ecdc4;
    font-weight: bold;
}

.empty-leaderboard {
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.7);
}

.empty-leaderboard .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-leaderboard {
    text-align: center;
    padding: 2rem;
}

.loading-leaderboard .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid #4ecdc4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* 关卡编辑器界面样式 */
.editor-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    grid-template-rows: 1fr;
    height: 100vh;
    background: #1a1a2e;
}

.editor-toolbar {
    background: rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    overflow-y: auto;
}

.toolbar-section {
    margin-bottom: 2rem;
}

.toolbar-section h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.tool-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.tool-btn.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.75rem;
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4CAF50;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.view-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-controls label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;
}

.view-controls input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.zoom-controls button {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

#zoom-level {
    color: white;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
}

.editor-main {
    position: relative;
    background: #16213e;
    overflow: hidden;
}

.editor-canvas-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#level-editor-canvas {
    display: block;
    cursor: crosshair;
    background: transparent;
}

.editor-properties {
    background: rgba(0, 0, 0, 0.3);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    overflow-y: auto;
}

.properties-section {
    margin-bottom: 2rem;
}

.properties-section h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    color: white;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.setting-item input,
.setting-item textarea,
.setting-item select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    color: white;
    font-size: 0.9rem;
}

.setting-item textarea {
    height: 60px;
    resize: vertical;
}

.setting-item input::placeholder,
.setting-item textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

#property-panel {
    color: white;
}

.property-item {
    margin-bottom: 0.5rem;
}

.property-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.property-item label:hover {
    background: rgba(255, 255, 255, 0.1);
}

.property-item input[type="radio"] {
    width: 16px;
    height: 16px;
}

.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: inline-block;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
}

.stat-item:last-child {
    border-bottom: none;
}

.editor-nav {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-1px);
}

/* 对话框样式 */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.dialog.hidden {
    display: none;
}

.dialog-content {
    background: #1a1a2e;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 2rem;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.dialog-content h3 {
    color: #4ecdc4;
    margin-bottom: 1.5rem;
    text-align: center;
}

.level-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1.5rem;
}

.level-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.level-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.level-item.selected {
    background: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.level-info h4 {
    color: white;
    margin-bottom: 0.25rem;
}

.level-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

.level-meta {
    text-align: right;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.dialog-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.dialog-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dialog-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dialog-btn.primary {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
}

/* 关卡编辑器移动端适配 */
@media (max-width: 1024px) {
    .editor-layout {
        grid-template-columns: 200px 1fr 250px;
    }

    .editor-toolbar {
        padding: 0.5rem;
    }

    .tool-buttons {
        grid-template-columns: 1fr;
    }

    .editor-properties {
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    .editor-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .editor-toolbar {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem;
        max-height: 200px;
        overflow-y: auto;
    }

    .toolbar-section {
        margin-bottom: 1rem;
    }

    .tool-buttons {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.25rem;
    }

    .tool-btn {
        padding: 0.5rem 0.25rem;
        font-size: 0.7rem;
    }

    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-btn {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .editor-properties {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        max-height: 250px;
        overflow-y: auto;
    }

    .properties-section {
        margin-bottom: 1rem;
    }

    .dialog-content {
        margin: 1rem;
        padding: 1rem;
        max-width: calc(100% - 2rem);
    }

    .level-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .level-meta {
        text-align: left;
        width: 100%;
    }

    .dialog-buttons {
        flex-direction: column;
    }

    .dialog-btn {
        width: 100%;
    }

    .editor-nav {
        position: relative;
        top: auto;
        right: auto;
        padding: 1rem;
        text-align: center;
    }

    #level-editor-canvas {
        cursor: default;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .game-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .menu-buttons {
        gap: 0.75rem;
    }

    .menu-btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .game-canvas {
        max-width: 100%;
        max-height: 60vh;
    }

    .game-ui {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .ui-section {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .ui-item {
        margin: 0;
    }

    .touch-controls {
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 1rem;
    }

    .touch-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .touch-btn:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
    }

    .level-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .level-card {
        padding: 1rem;
    }

    .player-list {
        grid-template-columns: 1fr;
    }

    .player-card {
        padding: 1rem;
    }

    .leaderboard-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .leaderboard-tab {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .leaderboard-entries {
        gap: 0.5rem;
    }

    .leaderboard-entry {
        padding: 0.75rem;
        grid-template-columns: auto 1fr auto;
        gap: 0.75rem;
    }

    .entry-rank {
        font-size: 0.9rem;
    }

    .entry-info h4 {
        font-size: 0.9rem;
    }

    .entry-info p {
        font-size: 0.7rem;
    }

    .entry-score {
        font-size: 0.9rem;
    }
}
